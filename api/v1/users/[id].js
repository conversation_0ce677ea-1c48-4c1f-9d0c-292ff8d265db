import admin from "../../../lib/firebase-admin.js";

export default async function handler(req, res) {
    const { id } = req.query;
    const user = await admin.auth().getUser(id).catch(() => null);

    if (!user) {
        res.status(404).json({ message: 'User not found' })
        return;
    }

    switch (req.method) {
        case 'GET':
            res.status(200).json({ name: user })
            break;
        case 'POST':
            res.status(200).json({ name: '<PERSON>' })
            break;
        case 'PUT':
            res.status(200).json({ name: '<PERSON>' })
            break;
        case 'DELETE':
            res.status(200).json({ name: '<PERSON>' })
            break;
        default:
            res.status(405).json({ message: 'Method not allowed' })
            break;
    }
}