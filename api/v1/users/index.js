import admin from "../../../lib/firebase-admin.js";

export default async function handler(req, res) {
    if (req.headers.authorization !== process.env.API_KEY) {
        return res.status(401).json({ message: 'Unauthorized' });
    }


    const users = await admin.auth().listUsers();
    switch (req.method) {
        case 'GET':
            return res.status(200).json(users);
        default:
            return res.status(405).end(`Method ${req.method} Not Allowed`);
    }
}
