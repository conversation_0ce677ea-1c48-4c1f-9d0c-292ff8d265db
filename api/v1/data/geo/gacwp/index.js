export default async function handler(req, res) {
    const { lon, lat, km } = req.query;
    const url = `https://public.opendatasoft.com/api/explore/v2.1/catalog/datasets/geonames-all-cities-with-a-population-1000/records?where=within_distance(coordinates, geom'{"type":"Point","coordinates":[${lon},${lat}]}', ${km}km)&limit=100&order_by=-population`;

    const data = await fetch(url).then((res) => res.json());
    res.status(200).json({ features: data });
}