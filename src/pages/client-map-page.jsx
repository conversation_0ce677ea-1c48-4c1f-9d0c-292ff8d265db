import { Colors, Drawer } from "@blueprintjs/core";
import { useEffect, useRef, useState } from "react";
import Map from "react-map-gl/mapbox";
import { useOutletContext } from "react-router-dom";
import GlobalMap from "../components/map/global-map";
import ClientRightSidebar from "../components/map/layout/client-right-sidebar";

export default function ClientMapPage() {
  const mapRef = useRef();
  const [center, setCenter] = useState(null);
  const [zoom, setZoom] = useState(null);
  const { theme, sidebarOpen } = useOutletContext();

  return (
    <div
      style={{
        flex: 1,
        borderTop: `1px solid ${
          theme === "light" ? Colors.LIGHT_GRAY1 : "rgba(255, 255, 255, 0.2)"
        }`,
        display: "flex",
        overflow: "hidden",
      }}
    >
      <div style={{ flex: 1 }}>
        <GlobalMap
          mapRef={mapRef}
          theme={theme}
          sidebarOpen={sidebarOpen}
          drawControl={false}
          actions={[setCenter, setZoom]}
        />
      </div>
      <div
        style={{
          width: 260,
          borderLeft: `1px solid ${
            theme === "light" ? Colors.LIGHT_GRAY1 : "rgba(255, 255, 255, 0.2)"
          }`,
          display: "flex",
          flexDirection: "column",
        }}
      >
        <ClientRightSidebar
          mapRef={mapRef}
          theme={theme}
          center={center}
          zoom={zoom}
        />
      </div>
    </div>
  );
}
