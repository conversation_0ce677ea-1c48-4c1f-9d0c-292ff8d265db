import { Route, Routes } from "react-router-dom";
import { globalContext } from "./global-context";
import { useContext } from "react";
import ClientContext from "./components/context/client-context";
import GlobalLayout from "./components/layout/global-layout";
import ClientLayout from "./components/layout/client-layout";
import ClientDashboardPage from "./pages/client-dashboard-page";
import ClientMapPage from "./pages/client-map-page";

function App() {
  const { loged } = useContext(globalContext);
  return (
    <Routes>
      <Route>
        {!loged ? (
          <Route>
            <Route element={<GlobalLayout />}>
              <Route element={<ClientLayout />}>
                <Route element={<ClientContext />}>
                  <Route path="" element={<ClientDashboardPage />} />
                  <Route path="map" element={<ClientMapPage />} />
                </Route>
              </Route>
              <Route>
                <Route element={"Internal layout"}>
                  <Route path="internal" element={"Internal context"}>
                    <Route path="" element={"Internal Dashboard"} />
                  </Route>
                </Route>
              </Route>
            </Route>
          </Route>
        ) : (
          <Route>
            <Route path="" element={"Public"} />
          </Route>
        )}
      </Route>
    </Routes>
  );
}

export default App;
