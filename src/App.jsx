import { Route, Routes } from "react-router-dom";
import { globalContext } from "./global-context";
import { useContext } from "react";
import ClientContext from "./components/context/client-context";
import GlobalLayout from "./components/layout/global-layout";
import ClientLayout from "./components/layout/client-layout";
import { clientMenuItems } from "./elements/client-menu-items";

function App() {
  const { loged } = useContext(globalContext);
  return (
    <Routes>
      <Route>
        {!loged ? (
          <Route>
            <Route element={<GlobalLayout />}>
              <Route element={<ClientLayout />}>
                <Route element={<ClientContext />}>
                  {clientMenuItems.map((item) => (
                    <Route
                      key={item.id}
                      path={item.path}
                      element={item.render}
                    />
                  ))}
                </Route>
              </Route>
              <Route>
                <Route element={"Internal layout"}>
                  <Route path="internal" element={"Internal context"}>
                    <Route path="" element={"Internal Dashboard"} />
                  </Route>
                </Route>
              </Route>
            </Route>
          </Route>
        ) : (
          <Route>
            <Route path="" element={"Public"} />
          </Route>
        )}
      </Route>
    </Routes>
  );
}

export default App;
