import { Colors, Menu, MenuItem } from "@blueprintjs/core";
import { Outlet, useNavigate, useOutletContext } from "react-router-dom";
import { clientMenuItems } from "../../elements/client-menu-items";

export default function ClientLayout() {
  const { theme, sidebarOpen, user } = useOutletContext();

  const navigate = useNavigate();
  return (
    <>
      <div
        style={{
          width: sidebarOpen ? 230 : 50,
          borderRight: `1px solid ${
            theme === "light" ? Colors.LIGHT_GRAY1 : "rgba(255, 255, 255, 0.2)"
          }`,
          paddingBlock: 10,
          overflow: "hidden",
        }}
      >
        <Menu
          className="sidebarMenu"
          style={{ padding: 0, backgroundColor: "transparent" }}
        >
          {clientMenuItems.map((item) => (
            <MenuItem
              key={item.id}
              text={sidebarOpen && item.title}
              icon={item.icon}
              onClick={() => navigate(item.path)}
              intent={location.pathname === item.path ? "primary" : "none"}
            />
          ))}
        </Menu>
      </div>
      <div
        style={{
          flex: 1,
          overflow: "hidden",
          display: "flex",
          flexDirection: "column",
        }}
      >
        <Outlet context={{ theme, sidebarOpen, user }} />
      </div>
    </>
  );
}
