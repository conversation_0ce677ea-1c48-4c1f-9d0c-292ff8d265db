import { useState } from "react";
import GlobalLoader from "../loader/global-loader";
import { Button, Colors } from "@blueprintjs/core";
import dayjs from "dayjs";
import Logo from "../assets/logo";
import { Outlet } from "react-router-dom";

export default function GlobalLayout() {
  const [user, setUser] = useState(null);
  const [theme, setTheme] = useState("light");
  const [sidebarOpen, setSidebarOpen] = useState(true);

  // if (!user) return <GlobalLoader />;

  return (
    <>
      <div
        className={theme}
        style={{
          display: "flex",
          flexDirection: "column",
          minHeight: "100svh",
          maxHeight: "100svh",
          overflow: "hidden",
          backgroundColor: theme === "light" ? "white" : Colors.DARK_GRAY2,
          borderTop: `1px solid ${
            theme === "light" ? Colors.LIGHT_GRAY1 : Colors.DARK_GRAY1
          }`,
        }}
      >
        <div
          style={{
            height: 50,
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
          }}
        >
          <div
            style={{
              height: 50,
              width: sidebarOpen ? 230 : 50,
              display: "flex",
              alignItems: "center",
              paddingInline: 15,
              borderRight: `1px solid ${
                theme === "light"
                  ? Colors.LIGHT_GRAY1
                  : "rgba(255, 255, 255, 0.2)"
              }`,
              justifyContent: "space-between",
            }}
          >
            {sidebarOpen && <Logo />}
            <Button
              icon={sidebarOpen ? "menu-closed" : "menu-open"}
              size="small"
              variant="minimal"
              onClick={() => setSidebarOpen(!sidebarOpen)}
            />
          </div>
          <div
            style={{
              flex: 1,
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
              paddingInline: "40px 15px",
            }}
          >
            <div>{dayjs().format("dddd MMMM D YYYY")}</div>
            <div>
              <Button
                icon={theme === "light" ? "moon" : "flash"}
                variant="minimal"
                onClick={() =>
                  setTheme(theme === "light" ? "bp6-dark" : "light")
                }
              />
              <Button icon="cog" variant="minimal" />
              <Button icon="notifications" variant="minimal" />
              <Button icon="user" variant="minimal" />
            </div>
          </div>
        </div>
        <div style={{ flex: 1, overflow: "hidden", display: "flex" }}>
          <Outlet context={{ user, theme, sidebarOpen }} />
        </div>
      </div>
    </>
  );
}
