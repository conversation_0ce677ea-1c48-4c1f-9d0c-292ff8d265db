import { useEffect, useState } from "react";
import { Layer, Source } from "react-map-gl/mapbox";
import { functions } from "../../../service/functions";

export default function MapAnalysisCitiesPop({ mapRef, center, radius }) {
  const [cities, setCities] = useState(null);

  useEffect(() => {
    if (center) {
      functions.data.geo.gacwp(center[0], center[1], radius).then((data) => {
        console.log(data);
        setCities(
          data.features.results.map((feature) => ({
            coordinates: [feature.coordinates.lon, feature.coordinates.lat],
            population: feature.population,
            name: feature.name,
          }))
        );
        console.log(cities);
      });
    }
  }, [center, radius]);

  if (!mapRef.current) return null;
  return (
    <>
      {cities && (
        <Source
          type="geojson"
          data={{
            type: "FeatureCollection",
            features: cities.map((city) => ({
              type: "Feature",
              properties: {
                population: city.population,
                name: city.name,
              },
              geometry: {
                type: "Point",
                coordinates: city.coordinates,
              },
            })),
          }}
        >
          <Layer
            id="population-circles"
            type="circle"
            paint={{
              "circle-radius": 5,
              "circle-color": [
                "interpolate",
                ["linear"],
                ["get", "population"],
                1000,
                "#ffffcc", // Jaune clair pour petites villes
                10000,
                "#a1dab4", // Vert clair
                50000,
                "#41b6c4", // Bleu clair
                100000,
                "#2c7fb8", // Bleu
                500000,
                "#253494", // Bleu foncé pour grandes villes
              ],
              "circle-opacity": [
                "interpolate",
                ["linear"],
                ["zoom"],
                0,
                0.5,
                10,
                1,
              ],
              "circle-stroke-width": 1,
              "circle-stroke-color": "#ffffff",
            }}
          />
        </Source>
      )}
    </>
  );
}
