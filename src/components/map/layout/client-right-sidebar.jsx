import { Colors } from "@blueprintjs/core";
import { useEffect, useState } from "react";

export default function ClientRightSidebar({ mapRef, theme }) {
  const [center, setCenter] = useState(null);
  const [zoom, setZoom] = useState(null);

  if (!mapRef.current) return null;

  mapRef.current.on("move", () => {
    setCenter(mapRef.current.getCenter());
    setZoom(mapRef.current.getZoom());
  });

  return (
    <>
      <div style={{ flex: 1 }}></div>
      <div
        style={{
          padding: 20,
          backgroundColor:
            theme === "light" ? Colors.LIGHT_GRAY5 : Colors.DARK_GRAY3,
          borderTop: `1px solid ${
            theme === "light" ? Colors.LIGHT_GRAY1 : "rgba(255, 255, 255, 0.2)"
          }`,
          fontFamily: "monospace",
          fontSize: 12,
        }}
      >
        {center && zoom && (
          <div>
            <div>
              Center: {center.lat}, {center.lng}
            </div>
            <div>Zoom: {zoom.toPrecision(2)}</div>
          </div>
        )}
      </div>
    </>
  );
}
