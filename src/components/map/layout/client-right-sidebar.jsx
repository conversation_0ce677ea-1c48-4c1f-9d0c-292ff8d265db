import { Colors } from "@blueprintjs/core";
import { useEffect, useState } from "react";

export default function ClientRightSidebar({ mapRef, theme, center, zoom }) {
  if (!mapRef.current) return null;

  return (
    <>
      <div style={{ flex: 1 }}></div>
      <div
        style={{
          padding: 20,
          backgroundColor:
            theme === "light" ? Colors.LIGHT_GRAY5 : Colors.DARK_GRAY3,
          borderTop: `1px solid ${
            theme === "light" ? Colors.LIGHT_GRAY1 : "rgba(255, 255, 255, 0.2)"
          }`,
          fontFamily: "monospace",
          fontSize: 12,
        }}
      >
        <div>
          <div>
            Lat: {center?.lat.toPrecision(6)}, Lng: {center?.lng.toPrecision(6)}
          </div>
          <div>Zoom: {zoom?.toPrecision(2)}</div>
        </div>
      </div>
    </>
  );
}
