import { SearchBox } from "@mapbox/search-js-react";
import { useEffect, useState } from "react";
import { Layer, Source } from "react-map-gl/mapbox";
import { functions } from "../../../service/functions";

export default function MapSearchControl({ mapRef, center }) {
  const [retrieve, setRetrieve] = useState(null);
  const [cities, setCities] = useState(null);

  // Fonction pour calculer le rayon basé sur la population
  const calculateRadius = (population) => {
    // Formule pour convertir la population en rayon (ajustable selon vos besoins)
    // Population de 1000 = rayon 5, population de 100000 = rayon 50
    const minRadius = 3;
    const maxRadius = 50;
    const minPop = 1000;
    const maxPop = 100000;

    if (population <= minPop) return minRadius;
    if (population >= maxPop) return maxRadius;

    // Échelle logarithmique pour une meilleure répartition visuelle
    const logPop = Math.log(population);
    const logMin = Math.log(minPop);
    const logMax = Math.log(maxPop);

    return (
      minRadius +
      ((logPop - logMin) / (logMax - logMin)) * (maxRadius - minRadius)
    );
  };

  useEffect(() => {
    if (retrieve) {
      functions.data.geo
        .gacwp(
          retrieve.features[0].geometry.coordinates[0],
          retrieve.features[0].geometry.coordinates[1]
        )
        .then((data) => {
          console.log(data);
          // Stocker les données complètes des villes avec population
          setCities(
            data.features.results.map((feature) => ({
              coordinates: [feature.coordinates.lon, feature.coordinates.lat],
              population: feature.population,
              name: feature.name,
              radius: calculateRadius(feature.population),
            }))
          );
          console.log("Cities with population:", cities);
        });
    }
  }, [retrieve]);
  return (
    <>
      <div
        style={{
          position: "absolute",
          top: 20,
          left: 40,
        }}
      >
        <SearchBox
          onRetrieve={(r) => setRetrieve(r)}
          theme={{
            variables: {
              boxShadow:
                "0 0 0 0 rgba(33,93,176,0),0 0 0 0 rgba(33,93,176,0),inset 0 0 0 1px rgba(17,20,24,.2),inset 0 1px 1px rgba(17,20,24,.3)",
              borderRadius: "2px",
            },
          }}
          accessToken={import.meta.env.VITE_MAPBOX_TOKEN}
          options={{
            proximity: {
              lat: center?.lat || 0,
              lng: center?.lng || 0,
            },
          }}
        />
        {cities && <div>Villes trouvées: {cities.length}</div>}
      </div>

      {cities && (
        <Source
          type="geojson"
          data={{
            type: "FeatureCollection",
            features: cities.map((city, index) => ({
              type: "Feature",
              properties: {
                population: city.population,
                name: city.name,
                radius: city.radius,
              },
              geometry: {
                type: "Point",
                coordinates: city.coordinates,
              },
            })),
          }}
        >
          <Layer
            id="population-circles"
            type="circle"
            paint={{
              "circle-radius": [
                "case",
                ["has", "radius"],
                ["get", "radius"],
                5, // rayon par défaut si pas de rayon défini
              ],
              "circle-color": [
                "interpolate",
                ["linear"],
                ["get", "population"],
                1000,
                "#ffffcc", // Jaune clair pour petites villes
                10000,
                "#a1dab4", // Vert clair
                50000,
                "#41b6c4", // Bleu clair
                100000,
                "#2c7fb8", // Bleu
                500000,
                "#253494", // Bleu foncé pour grandes villes
              ],
              "circle-opacity": 0.7,
              "circle-stroke-width": 1,
              "circle-stroke-color": "#ffffff",
            }}
          />
        </Source>
      )}
    </>
  );
}
