import { SearchBox } from "@mapbox/search-js-react";
import { useEffect, useState } from "react";
import { Layer, Source } from "react-map-gl/mapbox";
import { Button, ButtonGroup } from "@blueprintjs/core";
import { functions } from "../../../service/functions";
import MapAnalysisCitiesPop from "../analysis/map-analysis-cities-pop";

export default function MapSearchControl({ mapRef, center }) {
  const [retrieve, setRetrieve] = useState(null);
  return (
    <>
      <div
        style={{
          position: "absolute",
          top: 20,
          left: 40,
        }}
      >
        <SearchBox
          onClear={() => {
            setRetrieve(null);
          }}
          onRetrieve={(r) => setRetrieve(r)}
          theme={{
            variables: {
              boxShadow:
                "0 0 0 0 rgba(33,93,176,0),0 0 0 0 rgba(33,93,176,0),inset 0 0 0 1px rgba(17,20,24,.2),inset 0 1px 1px rgba(17,20,24,.3)",
              borderRadius: "2px",
            },
          }}
          accessToken={import.meta.env.VITE_MAPBOX_TOKEN}
          options={{
            proximity: {
              lat: center?.lat || 0,
              lng: center?.lng || 0,
            },
          }}
        />
      </div>
      {retrieve && (
        <MapAnalysisCitiesPop
          mapRef={mapRef}
          center={retrieve.features[0].geometry.coordinates}
          radius={50}
        />
      )}
    </>
  );
}
