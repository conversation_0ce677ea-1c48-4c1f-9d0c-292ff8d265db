import { SearchBox } from "@mapbox/search-js-react";
import { useEffect, useState } from "react";
import { Layer, Source } from "react-map-gl/mapbox";
import { functions } from "../../../service/functions";

export default function MapSearchControl({ mapRef, center }) {
  const [retrieve, setRetrieve] = useState(null);
  const [population, setPopulation] = useState(null);

  useEffect(() => {
    if (retrieve) {
      functions.data.geo
        .gacwp(
          retrieve.features[0].geometry.coordinates[0],
          retrieve.features[0].geometry.coordinates[1]
        )
        .then((data) => {
          console.log(data);
          setPopulation(
            data.features.results.map((feature) => feature.coordinates)
          );
          
        });
    }
  }, [retrieve]);
  return (
    <>
      <div
        style={{
          position: "absolute",
          top: 20,
          left: 40,
        }}
      >
        <SearchBox
          onRetrieve={(r) => setRetrieve(r)}
          theme={{
            variables: {
              boxShadow:
                "0 0 0 0 rgba(33,93,176,0),0 0 0 0 rgba(33,93,176,0),inset 0 0 0 1px rgba(17,20,24,.2),inset 0 1px 1px rgba(17,20,24,.3)",
              borderRadius: "2px",
            },
          }}
          accessToken={import.meta.env.VITE_MAPBOX_TOKEN}
          options={{
            proximity: {
              lat: center?.lat || 0,
              lng: center?.lng || 0,
            },
          }}
        />
        {population && population.lentgh}
      </div>

      {population && (
        <Source
          type="geojson"
          data={{
            type: "FeatureCollection",
            features: population.map((feature) => ({
              type: "Feature",
              geometry: {
                type: "Point",
                coordinates: feature,
              },
            })),
          }}
        >
          <Layer
            id="points"
            type="circle"
            paint={{
              "circle-radius": 5,
              "circle-color": "red",
            }}
          />
        </Source>
      )}
    </>
  );
}
