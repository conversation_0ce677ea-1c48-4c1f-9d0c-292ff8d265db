import { Button, ButtonGroup } from "@blueprintjs/core";

export default function MapGeneralControl({ mapRef }) {
  return (
    <>
      <div
        style={{
          position: "absolute",
          top: 20,
          right: 20,
          display: "flex",
          flexFlow: "column",
          gap: 15,
        }}
      >
        <Button icon="compass" />
        <ButtonGroup vertical>
          <Button icon="plus" onClick={() => mapRef.current?.zoomIn()} />
          <Button icon="minus" onClick={() => mapRef.current?.zoomOut()} />
        </ButtonGroup>
      </div>
    </>
  );
}
