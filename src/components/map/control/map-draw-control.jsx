import { Button } from "@blueprintjs/core";
import MapboxDraw from "@mapbox/mapbox-gl-draw";
import { useEffect, useRef } from "react";

export default function MapDrawControl({ mapRef }) {
  const draw = useRef(null);



  useEffect(() => {
    if (mapRef.current) {
      mapRef.current.resize();

      if (!draw.current) {
        draw.current = new MapboxDraw({
          displayControlsDefault: false,
        });
        mapRef.current.addControl(draw.current);

        draw.current = new MapboxDraw({
  defaultMode: "draw_circle",
  userProperties: true,
  modes: {
    ...MapboxDraw.modes,
    draw_circle  : <CircleMode></CircleMode>,
    drag_circle  : DragCircleMode,
    direct_select: DirectMode,
    simple_select: SimpleSelectMode
  }
});
      }
    }
  }, [mapRef]);

  // LINE
  const handleDrawLine = () => {
    if (draw.current) {
      draw.current.changeMode("draw_line_string");
    }
  };

  // POLYGON
  const handleDrawPolygon = () => {
    if (draw.current) {
      draw.current.changeMode("draw_polygon");
    }
  };
  // CIRCLE
  const handleDrawCircle = () => {
    if (draw.current) {
      draw.current.changeMode("draw_circle");
    }
  };
  return (
    <>
      <div style={{ position: "absolute", top: 10, left: 10 }}>
        <Button icon="draw" onClick={handleDrawLine} />
        <Button icon="polygon-filter" onClick={handleDrawPolygon} />
        <Button icon="circle" onClick={handleDrawCircle} />
        <Button icon="trash" onClick={() => draw.current.trash()} />
      </div>
    </>
  );
}
