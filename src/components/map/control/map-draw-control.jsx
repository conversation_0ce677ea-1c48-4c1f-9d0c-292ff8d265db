import {
  <PERSON><PERSON>,
  <PERSON>ton<PERSON>roup,
  <PERSON>u,
  <PERSON>u<PERSON>tem,
  <PERSON><PERSON>,
} from "@blueprintjs/core";
import MapboxDraw from "@mapbox/mapbox-gl-draw";
import { useEffect, useRef, useState } from "react";
import {
  CircleMode,
  DragCircleMode,
  DirectMode,
  SimpleSelectMode,
} from "mapbox-gl-draw-circle";

// CSS pour le curseur crosshair
const mapCrosshairStyle = `
  .mapboxgl-canvas-container.mapboxgl-interactive {
    cursor: crosshair !important;
  }
  .mapboxgl-canvas-container.mapboxgl-interactive.drag-circle-mode {
    cursor: crosshair !important;
  }
`;

export default function MapDrawControl({ mapRef }) {
  const draw = useRef(null);
  const styleElement = useRef(null);
  const [draws, setDraws] = useState([]);

  // Fonction pour ajouter le style crosshair
  const addCrosshairStyle = () => {
    if (!styleElement.current) {
      styleElement.current = document.createElement("style");
      styleElement.current.textContent = mapCrosshairStyle;
      document.head.appendChild(styleElement.current);
    }
  };

  // Fonction pour retirer le style crosshair
  const removeCrosshairStyle = () => {
    if (styleElement.current) {
      document.head.removeChild(styleElement.current);
      styleElement.current = null;
    }
  };

  useEffect(() => {
    if (mapRef.current) {
      mapRef.current.resize();

      if (!draw.current) {
        draw.current = new MapboxDraw({
          displayControlsDefault: false,
          modes: {
            ...MapboxDraw.modes,
            draw_circle: CircleMode,
            drag_circle: DragCircleMode,
            direct_select: DirectMode,
            simple_select: SimpleSelectMode,
          },
        });
        mapRef.current.addControl(draw.current);
      }
    }

    // Nettoyage lors du démontage du composant
    return () => {
      removeCrosshairStyle();
    };
  }, [mapRef]);

  // LINE
  const handleDrawLine = () => {
    if (draw.current) {
      addCrosshairStyle();
      draw.current.changeMode("draw_line_string");
    }
  };

  // POLYGON
  const handleDrawPolygon = () => {
    if (draw.current) {
      addCrosshairStyle();
      draw.current.changeMode("draw_polygon");
    }
  };

  // CIRCLE
  const handleDrawCircle = () => {
    if (draw.current) {
      addCrosshairStyle();
      draw.current.changeMode("drag_circle");
    }
  };

  // Fonction pour arrêter le dessin et retirer le curseur crosshair
  const handleStopDrawing = () => {
    if (draw.current) {
      removeCrosshairStyle();
      draw.current.changeMode("simple_select");
    }
  };

  const handleSaveDraw = () => {
    if (draw.current) {
      const data = draw.current.getAll();
      setDraws([...draws, data]);
    }
  };

  return (
    <>
      <div style={{ position: "absolute", bottom: 30, left: 40 }}>
        <ButtonGroup>
          <Button
            icon="floppy-disk"
            onClick={handleSaveDraw}
            title="Sauvegarder le dessin"
          />
          <Popover
            content={
              <Menu>
                {draws.map((draw, index) => (
                  <MenuItem
                    key={index}
                    text={`Draw ${index + 1}`}
                    onClick={() => console.log(draw)}
                  />
                ))}
              </Menu>
            }
          >
            <Button icon="list" />
          </Popover>
          <Button
            icon="draw"
            onClick={handleDrawLine}
            title="Dessiner une ligne"
          />
          <Button
            icon="polygon-filter"
            onClick={handleDrawPolygon}
            title="Dessiner un polygone"
          />
          <Button
            icon="circle"
            onClick={handleDrawCircle}
            title="Dessiner un cercle"
          />
          <Button
            icon="hand"
            onClick={handleStopDrawing}
            title="Arrêter le dessin"
          />
          <Button
            icon="trash"
            onClick={() => draw.current?.trash()}
            title="Supprimer la sélection"
          />
        </ButtonGroup>
      </div>
    </>
  );
}
