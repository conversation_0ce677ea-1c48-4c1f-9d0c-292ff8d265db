import { useEffect, useState } from "react";
import Map from "react-map-gl/mapbox";
import MapDrawControl from "./control/map-draw-control";
import MapGeneralControl from "./control/map-general-control";
import MapSearchControl from "./control/map-search-control";

export default function GlobalMap({
  children,
  mapRef,
  theme,
  sidebarOpen,
  drawControl = false,
  globalControl = true,
  searchControl = true,
  actions,
}) {
  const [mapStyle, setMapStyle] = useState(
    "mapbox://styles/tsinovec/cmdrg8u78001d01sd5evibo4x"
  );
  const [center, setCenter] = useState(null);

  useEffect(() => {
    setMapStyle(
      theme === "light"
        ? "mapbox://styles/tsinovec/cmdrg8u78001d01sd5evibo4x"
        : "mapbox://styles/tsinovec/cmdrfkyi5008801r5ckbg9zvf"
    );
  }, [theme]);

  useEffect(() => {
    if (mapRef.current) mapRef.current.resize();
  }, [sidebarOpen]);

  return (
    <>
      <Map
        onLoad={(target) => {
          mapRef.current = target.target;
        }}
        onMove={(e) => {
          actions[0](e.target.getCenter());
          actions[1](e.target.getZoom());
          setCenter(e.target.getCenter());
        }}
        projection={"mercator"}
        minZoom={2}
        ref={mapRef}
        logoPosition="bottom-right"
        attributionControl={false}
        mapboxAccessToken={import.meta.env.VITE_MAPBOX_TOKEN}
        mapStyle={mapStyle}
      >
        {searchControl && <MapSearchControl mapRef={mapRef} center={center} />}
        {drawControl && <MapDrawControl mapRef={mapRef} />}
        {globalControl && <MapGeneralControl mapRef={mapRef} />}
        {children}
      </Map>
    </>
  );
}
