import { useEffect, useState } from "react";
import Map from "react-map-gl/mapbox";
import MapDrawControl from "./control/map-draw-control";

export default function GlobalMap({
  children,
  mapRef,
  theme,
  sidebarOpen,
  draw = false,
}) {
  const [mapStyle, setMapStyle] = useState(
    "mapbox://styles/tsinovec/cmdrg8u78001d01sd5evibo4x"
  );

  useEffect(() => {
    setMapStyle(
      theme === "light"
        ? "mapbox://styles/tsinovec/cmdrg8u78001d01sd5evibo4x"
        : "mapbox://styles/tsinovec/cmdrfkyi5008801r5ckbg9zvf"
    );
  }, [theme]);

  useEffect(() => {
    if (mapRef.current) mapRef.current.resize();
  }, [sidebarOpen]);

  return (
    <>
      <Map
        projection={"mercator"}
        minZoom={2}
        ref={mapRef}
        onLoad={(e) => (mapRef.current = e.target)}
        logoPosition="bottom-right"
        attributionControl={false}
        mapboxAccessToken={import.meta.env.VITE_MAPBOX_TOKEN}
        mapStyle={mapStyle}
      >
        {draw && <MapDrawControl mapRef={mapRef} />}
        {children}
      </Map>
    </>
  );
}
