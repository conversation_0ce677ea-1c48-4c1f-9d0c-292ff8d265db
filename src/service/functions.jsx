export const functions = {
  users: {
    async: {
      listUsers: async () => {
        const users = (
          await fetch(`${location.origin}/api/v1/users`, {
            headers: {
              Accept: "application/json",
              "Content-Type": "application/json",
              Authorization: `${import.meta.env.VITE_API_KEY}`,
            },
          })
        ).json();
        return users;
      },
      getUser: async (id) => {
        const user = (
          await fetch(`${location.origin}/api/v1/users/${id}`, {
            headers: {
              Accept: "application/json",
              "Content-Type": "application/json",
              Authorization: `${import.meta.env.VITE_API_KEY}`,
            },
          })
        ).json();
        return user;
      },
      createUser: async (user) => {
        const newUser = (
          await fetch(`${location.origin}/api/v1/users`, {
            method: "POST",
            headers: {
              Accept: "application/json",
              "Content-Type": "application/json",
              Authorization: `${import.meta.env.VITE_API_KEY}`,
            },
            body: JSON.stringify(user),
          })
        ).json();
        return newUser;
      },
      updateUser: async (user) => {
        const updatedUser = (
          await fetch(`${location.origin}/api/v1/users/${user.id}`, {
            method: "PUT",
            headers: {
              Accept: "application/json",
              "Content-Type": "application/json",
              Authorization: `${import.meta.env.VITE_API_KEY}`,
            },
            body: JSON.stringify(user),
          })
        ).json();
        return updatedUser;
      },
      deleteUser: async (id) => {
        const deletedUser = (
          await fetch(`${location.origin}/api/v1/users/${id}`, {
            method: "DELETE",
            headers: {
              Accept: "application/json",
              "Content-Type": "application/json",
              Authorization: `${import.meta.env.VITE_API_KEY}`,
            },
          })
        ).json();
        return deletedUser;
      },
    },
  },
  data: {
    geo: {
      frlepf: async (q = "fives") => {
        const data = (
          await fetch(`${location.origin}/api/v1/data/geo/fr-lepf?q=${q}`, {
            headers: {
              Accept: "application/json",
              "Content-Type": "application/json",
            },
          })
        ).json();
        return data;
      },
      gacwp: async (lon, lat, km = 10) => {
        const data = (
          await fetch(
            `${location.origin}/api/v1/data/geo/gacwp?lon=${lon}&lat=${lat}&km=${km}`,
            {
              headers: {
                Accept: "application/json",
                "Content-Type": "application/json",
              },
            }
          )
        ).json();
        return data;
      },
    },
  },
};
