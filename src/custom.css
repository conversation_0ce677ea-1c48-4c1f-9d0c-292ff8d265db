body ::selection {
  background: #af007c;
  color: white;
}

.sidebarMenu .bp6-menu-item {
  padding-inline: 15px;
  height: 35px;
  display: flex;
  align-items: center;
  font-weight: 500;
  gap: 5px;
  border-radius: 0px;
}

.sidebarMenu .bp6-intent-primary {
  color: #af007c !important;
}

.sidebarMenu .bp6-intent-primary:hover {
  background-color: #af007c10 !important;
}

.sidebarMenu .bp6-intent-primary:active {
  background-color: #af007c20 !important;
}

.site-marker:hover {
  color: #af007c !important;
  cursor: pointer;
  transition: all 0.2s ease;
  scale: 1.3;
}

.site-marker:active {
  color: #7f005b !important;
}


.fc-scrollgrid thead th {
  background: transparent !important;
}